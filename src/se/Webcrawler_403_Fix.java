package LTM_04;

import java.io.FileWriter;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Scanner;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Webcrawler_403_Fix {
    private final Set<String> visitedUrls;
    private final HttpClient http;

    public Webcrawler_403_Fix() {
        visitedUrls = new HashSet<>();
        http = HttpClient.newBuilder()
                .followRedirects(HttpClient.Redirect.NORMAL)
                .connectTimeout(Duration.ofSeconds(10))
                .build();
    }

    public void crawlWebsite(String url, int depth) {
        visitedUrls.clear();
        crawlUrl(url, depth);
    }

    private void crawlUrl(String url, int depth) {
        if (visitedUrls.contains(url)) return;
        visitedUrls.add(url);

        String html = fetchHtml(url);
        if (html == null) return;

        if (depth > 0) {
            Set<String> childUrls = getChildUrls(url, html);

            Iterator<String> iterator = childUrls.iterator();
            while (iterator.hasNext()) {
                String childUrl = iterator.next();
                if (visitedUrls.contains(childUrl)) {
                    iterator.remove();
                }
            }
            for (String childUrl : childUrls) {
                crawlUrl(childUrl, depth - 1);
            }
        }
    }

    private String fetchHtml(String url) {
        try {
            HttpRequest req = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(15))
                    .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) " +
                            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                    .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
                    .header("Accept-Language", "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7")
                    .GET()
                    .build();

            HttpResponse<String> res = http.send(req, HttpResponse.BodyHandlers.ofString());
            int status = res.statusCode();
            if (status == 200) {
                return res.body();
            } else {
                System.out.println("HTTP " + status + " khi tải: " + url);
                return null;
            }
        } catch (Exception e) {
            System.out.println("Lỗi tải URL (" + url + "): " + e.getMessage());
            return null;
        }
    }

    private Set<String> getChildUrls(String parentUrl, String html) {
        Set<String> childUrls = new HashSet<>();
        Pattern pattern = Pattern.compile("<a\\s+[^>]*href\\s*=\\s*\"([^\"]+)\"", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(html);

        while (matcher.find()) {
            String childUrl = matcher.group(1).trim();

            if (childUrl.isEmpty()
                    || childUrl.startsWith("#")
                    || childUrl.startsWith("mailto:")
                    || childUrl.startsWith("javascript:")
                    || childUrl.startsWith("tel:")) {
                continue;
            }

            String normalized = normalizeUrl(childUrl, parentUrl);
            if (normalized != null) {
                childUrls.add(normalized);
            }
        }
        return childUrls;
    }

    private String normalizeUrl(String childUrl, String parentUrl) {
        try {
            URL base = new URL(parentUrl);
            URL absolute = new URL(base, childUrl);

            String protocol = absolute.getProtocol();
            if (!protocol.equals("http") && !protocol.equals("https")) return null;

            String path = absolute.getPath();
            if (path == null || path.isEmpty()) path = "/";

            String normalizedUrl = protocol + "://" + absolute.getHost();
            if (absolute.getPort() != -1 && absolute.getPort() != absolute.getDefaultPort()) {
                normalizedUrl += ":" + absolute.getPort();
            }
            normalizedUrl += path;

            if (normalizedUrl.endsWith("/") && !"/".equals(path)) {
                normalizedUrl = normalizedUrl.substring(0, normalizedUrl.length() - 1);
            }
            return normalizedUrl;
        } catch (MalformedURLException e) {
            return null;
        }
    }

    public void saveVisitedUrlsToFile(String filename) {
        try (FileWriter writer = new FileWriter(filename)) {
            for (String url : visitedUrls) {
                writer.write(url + System.lineSeparator());
            }
        } catch (IOException e) {
            System.out.println("Lỗi khi lưu file: " + e.getMessage());
        }
    }

    private static String getWebsiteName(String websiteUrl) {
        try {
            URL u = new URL(websiteUrl);
            String host = u.getHost();
            String[] parts = host.split("\\.");
            if (parts.length >= 2) return parts[parts.length - 2];
            return host.replaceAll("\\W+", "_");
        } catch (MalformedURLException e) {
            return "site";
        }
    }

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter the website URL: ");
        String websiteUrl = scanner.nextLine().trim();

        System.out.print("Enter the crawl depth: ");
        int crawlDepth = scanner.nextInt();
        scanner.nextLine();

        String websiteName = getWebsiteName(websiteUrl);
        String outputFileName = websiteName + "_urls.txt";

        System.out.println("Crawling " + websiteUrl + " ... (depth=" + crawlDepth + ")");
        Webcrawler_403_Fix webCrawler = new Webcrawler_403_Fix();
        webCrawler.crawlWebsite(websiteUrl, crawlDepth);
        webCrawler.saveVisitedUrlsToFile(outputFileName);

        System.out.println("Visited URLs saved to file: " + outputFileName);
    }
}

